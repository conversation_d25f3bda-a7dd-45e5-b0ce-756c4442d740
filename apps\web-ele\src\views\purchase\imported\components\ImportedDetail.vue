<script lang="ts" setup>
import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

// 导入 Element Plus 组件
import {
  ElNotification,
  ElTabPane as TabPane,
  ElTabs as Tabs,
} from 'element-plus';

import { editCompany, getImportedInfo } from '#/api/purchase/imported';
import { TableAction } from '#/components/table-action';
import { $t } from '#/locales';

// 导入页签组件
import BasicInfoTab from './BasicInfoTab.vue';
import CustomsInfoTab from './CustomsInfoTab.vue';

/**
 * 状态变量
 * isUpdate: 是否为更新模式
 * isView: 是否为查看模式
 * record: 当前记录数据
 */
const isUpdate = ref(false);
const isView = ref(false);
const record = ref<any>({});

// 页签相关状态
const activeTab = ref('1'); // 默认显示基本信息页签

// 页签切换处理
const handleChange = (key: any) => {
  // 页签切换逻辑
  if (key === '2') {
    // 切换到报关信息页签时的处理
  }
};

// 页签组件引用
const basicInfoTabRef = ref();
const customsInfoTabRef = ref();

// 获取基本信息表单API
const getBasicFormApi = () => {
  return basicInfoTabRef.value?.formApi;
};

// 获取报关信息表单API
const getCustomsFormApi = () => {
  return customsInfoTabRef.value?.formApi;
};

/**
 * 按钮处理函数
 */
// 处理编辑按钮点击
const handleEdit = () => {
  isUpdate.value = true;
};

// 处理保存按钮点击
const handleSave = async () => {
  try {
    // 收集报关单信息数据
    const customsData: any = {
      orderNo: record.value.orderNo,
      id: record.value.id,
    };

    // 从基础信息表单获取 declarationNo
    const basicFormApi = getBasicFormApi();
    if (basicFormApi) {
      const basicData = await basicFormApi.getValues();
      customsData.declarationNo = basicData.declarationNo;
      customsData.supplier = basicData.supplier;
      customsData.remark = basicData.remark;
    }

    // 收集各个报关单表单的数据
    const customsFormApi = getCustomsFormApi();
    if (customsFormApi) {
      // 进口公司信息
      if (customsFormApi.importCompany) {
        const importCompanyData =
          await customsFormApi.importCompany.getValues();
        Object.assign(customsData, importCompanyData);
      }

      // 出口公司信息
      if (customsFormApi.exportCompany) {
        const exportCompanyData =
          await customsFormApi.exportCompany.getValues();
        Object.assign(customsData, exportCompanyData);
      }

      // 提单信息
      if (customsFormApi.billOfLading) {
        const billOfLadingData = await customsFormApi.billOfLading.getValues();
        Object.assign(customsData, billOfLadingData);
      }

      // 许可证信息
      if (customsFormApi.permit) {
        const permitData = await customsFormApi.permit.getValues();
        Object.assign(customsData, permitData);
      }

      // 发票信息
      if (customsFormApi.invoice) {
        const invoiceData = await customsFormApi.invoice.getValues();
        Object.assign(customsData, invoiceData);
      }

      // 税务信息
      if (customsFormApi.tax) {
        const taxData = await customsFormApi.tax.getValues();
        Object.assign(customsData, taxData);
      }
    }

    // 调用报关单信息保存API
    await editCompany(customsData);
    isUpdate.value = false;
    ElNotification({
      type: 'success',
      message: '保存成功',
      duration: 2500,
    });
  } catch {
    ElNotification({
      type: 'error',
      message: '保存失败',
      duration: 2500,
    });
  }
};

// 处理取消编辑按钮点击
const handleCancelEdit = async () => {
  isUpdate.value = false;
  // 重新加载数据来重置表单
  if ((isUpdate.value || isView.value) && record.value.id) {
    try {
      // 重新获取数据
      const res = await getImportedInfo({ id: record.value.id });
      if (res) {
        // 确定数据结构，兼容不同的返回格式
        const data = res.value || res;
        // 判断状态是否是新建状态，只有新建状态才可以允许修改
        // isView.value = data.status !== 'new';

        // 重新设置基本信息表单值
        const basicFormApi = getBasicFormApi();
        if (basicFormApi) {
          basicFormApi.setValues(data);
        }
        // 重新设置报关信息表单值
        const customsFormApi = getCustomsFormApi();
        if (customsFormApi) {
          // 获取报关信息数据，支持多种数据结构
          const customsData = data.customsDeclaration || data;

          // 重新设置各个子表单的值
          if (customsFormApi.exportCompany) {
            customsFormApi.exportCompany.setValues(customsData);
          }
          if (customsFormApi.importCompany) {
            customsFormApi.importCompany.setValues(customsData);
          }
          if (customsFormApi.billOfLading) {
            customsFormApi.billOfLading.setValues(customsData);
          }
          if (customsFormApi.permit) {
            customsFormApi.permit.setValues(customsData);
          }
          if (customsFormApi.invoice) {
            customsFormApi.invoice.setValues(customsData);
          }
          if (customsFormApi.tax) {
            customsFormApi.tax.setValues(customsData);
          }
        }
      }
    } catch {
      ElNotification({
        duration: 2500,
        message: '重新加载数据失败',
        type: 'error',
      });
    }
  }
};

/**
 * 计算弹窗标题
 * 根据当前模式（新增/编辑/查看）返回对应的标题
 */
const title = computed(() => {
  if (isView.value) {
    return $t('purchase.viewImported'); // 查看国外进口
  }
  return isUpdate.value
    ? $t('purchase.editImported')
    : $t('purchase.addImported'); // 编辑国外进口 : 添加国外进口
});

/**
 * 创建模态框
 * 配置模态框的行为和事件处理
 */
const [Modal, modalApi] = useVbenModal({
  closeOnClickModal: false, // 点击遮罩层不关闭
  draggable: true, // 可拖动
  showConfirmButton: false,
  showCancelButton: true,
  destroyOnClose: true,
  loading: true, // 初始为 true
  cancelText: $t('basic.close'),
  // 打开状态变化时的回调
  onOpenChange(isOpen) {
    if (isOpen) {
      modalApi.setState({ loading: true }); // 设置模态框 loading 状态
      // 打开时获取数据
      record.value = modalApi.getData()?.record || {};
      isUpdate.value = modalApi.getData()?.isUpdate || false;
      isView.value = modalApi.getData()?.isView || false;

      // 如果是编辑或查看模式，且有ID，则获取详细信息
      if ((isUpdate.value || isView.value) && record.value.id) {
        try {
          // 使用单个 nextTick 确保子组件已经挂载
          getImportedInfo({ id: record.value.id }).then((res) => {
            if (res) {
              // 确定数据结构，兼容不同的返回格式
              const data = res.value || res;

              // 设置基本信息表单值
              const basicFormApi = getBasicFormApi();
              if (basicFormApi) {
                basicFormApi.setValues(data);
              }

              // 设置报关信息表单值
              const customsFormApi = getCustomsFormApi();
              if (customsFormApi) {
                // 获取报关信息数据，支持多种数据结构
                const customsData = data.customsDeclaration || data;

                // 设置各个子表单的值
                if (customsFormApi.exportCompany) {
                  customsFormApi.exportCompany.setValues(customsData);
                }
                if (customsFormApi.importCompany) {
                  customsFormApi.importCompany.setValues(customsData);
                }
                if (customsFormApi.billOfLading) {
                  customsFormApi.billOfLading.setValues(customsData);
                }
                if (customsFormApi.permit) {
                  customsFormApi.permit.setValues(customsData);
                }
                if (customsFormApi.invoice) {
                  customsFormApi.invoice.setValues(customsData);
                }
                if (customsFormApi.tax) {
                  customsFormApi.tax.setValues(customsData);
                }
              }
            }
          });
        } catch {
          ElNotification({ duration: 2500, message: 'error', type: 'error' });
        }
      } else {
        // 新增模式，重置表单
        const basicFormApi = getBasicFormApi();
        if (basicFormApi) {
          basicFormApi.resetForm();
        }
        const customsFormApi = getCustomsFormApi();
        if (customsFormApi) {
          // 重置各个子表单
          if (customsFormApi.exportCompany) {
            customsFormApi.exportCompany.resetForm();
          }
          if (customsFormApi.importCompany) {
            customsFormApi.importCompany.resetForm();
          }
          if (customsFormApi.billOfLading) {
            customsFormApi.billOfLading.resetForm();
          }
          if (customsFormApi.permit) {
            customsFormApi.permit.resetForm();
          }
          if (customsFormApi.invoice) {
            customsFormApi.invoice.resetForm();
          }
          if (customsFormApi.tax) {
            customsFormApi.tax.resetForm();
          }
        }
      }
      modalApi.setState({ loading: false });
    } else {
      // 关闭时重置状态
      isUpdate.value = false;
      record.value = {};
      activeTab.value = '1'; // 重置页签到基本信息
    }
  },
  onCancel() {
    modalApi.close();
  },
  onConfirm() {},
});

/**
 * 暴露组件方法
 * 直接暴露 modalApi，包含所有需要的方法
 */
defineExpose({
  modalApi,
});
</script>

<template>
  <Modal class="h-[90%] w-[90%]" :title="title">
    <div class="flex h-full flex-col">
      <Tabs
        tab-position="top"
        v-model="activeTab"
        @change="handleChange"
        class="sticky-tabs"
      >
        <!-- 基本信息页签 -->
        <TabPane label="基本信息" name="1" class="h-full">
          <BasicInfoTab
            ref="basicInfoTabRef"
            :record="record"
            :is-update="isUpdate"
          />
        </TabPane>

        <!-- 报关信息页签 -->
        <TabPane label="报关信息" name="2" class="h-full">
          <CustomsInfoTab
            ref="customsInfoTabRef"
            :record="record"
            :is-update="isUpdate"
          />
        </TabPane>
      </Tabs>
    </div>

    <!-- Footer 插槽 -->
    <template #center-footer>
      <TableAction
        :actions="[
          // 查看模式下的编辑按钮
          ...(isView && !isUpdate
            ? [
                {
                  label: '编辑',
                  type: 'primary' as const,
                  onClick: handleEdit,
                },
              ]
            : []),
          // 编辑模式下的取消编辑和保存按钮
          ...(isUpdate
            ? [
                {
                  label: '取消编辑',
                  onClick: handleCancelEdit,
                },
                {
                  label: '保存',
                  type: 'primary' as const,
                  onClick: handleSave,
                },
              ]
            : []),
          // 新增模式下的保存按钮
          ...(!isView && !isUpdate
            ? [
                {
                  label: '保存',
                  type: 'primary' as const,
                  onClick: handleSave,
                },
              ]
            : []),
        ]"
      />
    </template>
  </Modal>
</template>

<style scoped>
/* 强制Element Plus tabs正确布局 - 使用!important确保优先级 */
.sticky-tabs :deep(.el-tabs) {
  display: flex !important;
  flex-direction: column !important;
  height: 100% !important;
}

/* 强制页签头部在顶部并固定 */
.sticky-tabs :deep(.el-tabs__header) {
  position: sticky !important;
  top: 0 !important;
  margin: 0 !important;
}

/* 页签导航栏样式 */
.sticky-tabs :deep(.el-tabs__nav-wrap) {
  margin-bottom: 0 !important;
}

/* 页签内容区域 */
.sticky-tabs :deep(.el-tabs__content) {
  flex: 1 !important;
  order: 1 !important;
  overflow-y: auto !important;
}

/* 确保页签容器正确布局 */
.sticky-tabs {
  flex-direction: column !important;
  height: 100% !important;
}
</style>
